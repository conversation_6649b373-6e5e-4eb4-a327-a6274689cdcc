{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"my-app": {"root": "", "sourceRoot": "src", "projectType": "application", "prefix": "app", "schematics": {"@schematics/angular:pipe": {"lintFix": true}, "@schematics/angular:class": {"lintFix": true}, "@schematics/angular:module": {"lintFix": true}, "@schematics/angular:service": {"lintFix": true}, "@schematics/angular:component": {"lintFix": true, "style": "less"}, "@schematics/angular:directive": {"lintFix": true}}, "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/my-app", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets", {"glob": "**/*", "input": "./node_modules/@ant-design/icons-angular/src/inline-svg/", "output": "/assets/"}], "styles": ["node_modules/ng-zorro-antd/ng-zorro-antd.min.css", "src/styles.less", "src/assets/iconfont/iconfont.css", "node_modules/bootstrap/dist/css/bootstrap.min.css", "src/app/style/common/_reset.less", "node_modules/bpmn-js/dist/assets/diagram-js.css", "node_modules/bpmn-js/dist/assets/bpmn-font/css/bpmn.css", "node_modules/@bpmn-io/properties-panel/assets/properties-panel.css"], "stylePreprocessorOptions": {"includePaths": ["src/app/style"]}, "preserveSymlinks": true, "aot": true, "vendorChunk": true, "extractLicenses": false, "buildOptimizer": false, "sourceMap": true, "optimization": false, "namedChunks": true, "scripts": ["src/assets/js/jquery/jquery.min.js"], "allowedCommonJsDependencies": ["@angularclass/hmr", "highlight.js", "json2yaml", "src/app/components/console/dashboard-template/map-chart-template/map-chart-template.component", "file-saver", "clipboard", "classnames"]}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}, {"replace": "src/app/modules/console/console-routing.module.ts", "with": "src/app/modules/console/console-routing.module.prod.ts"}, {"replace": "src/app/modules/console/user/user-routing.module.ts", "with": "src/app/modules/console/user/user-routing.module.prod.ts"}, {"replace": "src/app/components/console/common/console-layout/menuData.ts", "with": "src/app/components/console/common/console-layout/menuData.prod.ts"}, {"replace": "src/app/components/console/common/console-sub-menu/subMenuData.ts", "with": "src/app/components/console/common/console-sub-menu/subMenuData.prod.ts"}, {"replace": "src/app/components/console/common/console-nav/navData.ts", "with": "src/app/components/console/common/console-nav/navData.prod.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "budgets": [{"type": "initial", "maximumWarning": "4mb", "maximumError": "8mb"}, {"type": "anyComponentStyle", "maximumWarning": "20kb", "maximumError": "40kb"}]}, "test": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.test.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "20kb", "maximumError": "40kb"}]}, "k8s": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.k8s.ts"}], "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "20kb", "maximumError": "40kb"}]}, "hmr": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.hmr.ts"}]}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"buildTarget": "my-app:build"}, "configurations": {"production": {"buildTarget": "my-app:build:production"}, "test": {"buildTarget": "my-app:build:test"}, "hmr": {"hmr": true, "buildTarget": "my-app:build:hmr", "hmrWarning": false}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "my-app:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "src/tsconfig.spec.json", "karmaConfig": "src/karma.conf.js", "styles": ["src/styles.less"], "scripts": [], "assets": ["src/favicon.ico", "src/assets"]}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"tsConfig": ["src/tsconfig.app.json", "src/tsconfig.spec.json"], "exclude": ["**/node_modules/**"]}}}}, "my-app-e2e": {"root": "e2e/", "projectType": "application", "architect": {"e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "my-app:serve"}, "configurations": {"production": {"devServerTarget": "my-app:serve:production"}}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"tsConfig": "e2e/tsconfig.e2e.json", "exclude": ["**/node_modules/**"]}}}}}, "cli": {"analytics": "cd9c15f7-b8ea-4336-b8f1-2f5d53d6652a", "schematicCollections": ["@angular/cli"]}}