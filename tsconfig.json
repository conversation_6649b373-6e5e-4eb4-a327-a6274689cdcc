{"extends": "./tsconfig.base.json", "compilerOptions": {"baseUrl": "./", "outDir": "./dist/out-tsc", "sourceMap": true, "declaration": false, "downlevelIteration": true, "experimentalDecorators": true, "module": "es2022", "moduleResolution": "bundler", "importHelpers": true, "target": "ES2022", "typeRoots": ["node_modules/@types"], "lib": ["es2022", "dom"], "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "useDefineForClassFields": false, "esModuleInterop": true, "skipLibCheck": true}, "exclude": ["node_modules/bpmn-js/**/*", "node_modules/diagram-js/**/*"], "angularCompilerOptions": {"fullTemplateTypeCheck": true, "strictInjectionParameters": true, "enableIvy": true, "strictTemplates": false, "strictInputAccessModifiers": true}}