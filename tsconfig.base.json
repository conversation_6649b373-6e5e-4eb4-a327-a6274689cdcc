{"compileOnSave": false, "compilerOptions": {"baseUrl": "./", "paths": {"@assets/*": ["src/assets/*"]}, "downlevelIteration": true, "outDir": "./dist/out-tsc", "sourceMap": true, "declaration": false, "module": "es2022", "moduleResolution": "bundler", "emitDecoratorMetadata": true, "experimentalDecorators": true, "target": "es2022", "skipLibCheck": true, "typeRoots": ["node_modules/@types"], "lib": ["es2022", "dom"], "esModuleInterop": true, "useDefineForClassFields": false}, "exclude": ["node_modules/bpmn-js/lib/model/Types.ts", "node_modules/diagram-js/lib/core/Types.ts", "node_modules/diagram-js/lib/model/Types.ts", "node_modules/diagram-js/lib/util/Types.ts"]}