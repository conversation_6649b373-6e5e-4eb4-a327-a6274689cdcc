{"extends": "../tsconfig.base.json", "compilerOptions": {"outDir": "../out-tsc/app", "types": ["node"]}, "files": ["main.ts", "polyfills.ts"], "include": ["src/**/*.ts", "src/**/*.d.ts"], "exclude": ["**/disaster-recovery/**/*.ts", "**/console/disaster-recovery/**/*.ts", "node_modules/bpmn-js/lib/model/Types.ts", "node_modules/diagram-js/lib/core/Types.ts", "node_modules/diagram-js/lib/model/Types.ts", "node_modules/diagram-js/lib/util/Types.ts"]}